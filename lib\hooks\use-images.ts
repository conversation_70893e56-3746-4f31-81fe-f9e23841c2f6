"use client";

import {
   deleteImageAction,
   uploadImage,
   uploadMultipleImages,
} from "@/lib/actions/upload-actions";
import { PaginationOptions, UpdateImageInput } from "@/lib/models";
import {
   getImageById,
   getImages,
   getUngroupedImages,
   searchImages,
   updateImage,
} from "@/lib/services/image-service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { invalidationKeys, queryKeys } from "./query-keys";

/**
 * Hook to fetch images with pagination and filtering
 */
export function useImages(
   options: PaginationOptions & {
      albumId?: string | null;
      collectionId?: string | null;
   } = {}
) {
   return useQuery({
      queryKey: queryKeys.images.list(options),
      queryFn: () => getImages(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch ungrouped images
 */
export function useUngroupedImages(options: PaginationOptions = {}) {
   return useQuery({
      queryKey: queryKeys.images.ungrouped(options),
      queryFn: () => getUngroupedImages(options),
      staleTime: 5 * 60 * 1000, // 5 minutes
   });
}

/**
 * Hook to fetch a single image by ID
 */
export function useImage(id: string) {
   return useQuery({
      queryKey: queryKeys.images.detail(id),
      queryFn: () => getImageById(id),
      enabled: !!id,
      staleTime: 10 * 60 * 1000, // 10 minutes
   });
}

/**
 * Hook to search images
 */
export function useSearchImages(
   query: string,
   options: PaginationOptions = {}
) {
   return useQuery({
      queryKey: queryKeys.images.search(query, options),
      queryFn: () => searchImages(query, options),
      enabled: !!query && query.length > 0,
      staleTime: 2 * 60 * 1000, // 2 minutes
   });
}

/**
 * Hook to upload a single image
 */
export function useUploadImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: uploadImage,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate relevant queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            toast.success(data.message || "Image uploaded successfully");
         } else {
            toast.error(data.error || "Failed to upload image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to upload image"
         );
      },
   });
}

/**
 * Hook to upload multiple images
 */
export function useUploadMultipleImages() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: uploadMultipleImages,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate relevant queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            toast.success(data.message || "Images uploaded successfully");
         } else {
            toast.error(data.error || "Failed to upload images");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to upload images"
         );
      },
   });
}

/**
 * Hook to update image metadata
 */
export function useUpdateImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: ({ id, input }: { id: string; input: UpdateImageInput }) =>
         updateImage(id, input),
      onSuccess: (data, variables) => {
         if (data) {
            // Update the specific image in cache
            queryClient.setQueryData(
               queryKeys.images.detail(variables.id),
               data
            );

            // Invalidate lists to ensure consistency
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.imagesList(),
            });

            toast.success("Image updated successfully");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to update image"
         );
      },
   });
}

/**
 * Hook to delete an image
 */
export function useDeleteImage() {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: deleteImageAction,
      onSuccess: (data) => {
         if (data.success) {
            // Invalidate all image queries
            queryClient.invalidateQueries({
               queryKey: invalidationKeys.allImages(),
            });
            toast.success(data.message || "Image deleted successfully");
         } else {
            toast.error(data.error || "Failed to delete image");
         }
      },
      onError: (error) => {
         toast.error(
            error instanceof Error ? error.message : "Failed to delete image"
         );
      },
   });
}
