"use server";

import {
   Album,
   AlbumWithStats,
   CreateAlbumInput,
   DEFAULT_PAGINATION,
   PaginatedResponse,
   PaginationOptions,
   UpdateAlbumInput,
   createAlbumMetadata,
   createPaginationMetadata,
   validateAlbumInput,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get all albums with pagination
 */
export async function getAlbums(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<AlbumWithStats>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const albumCollection = await getCollection<Album>("albums");

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Get albums with image counts
      const pipeline = [
         {
            $lookup: {
               from: "images",
               localField: "_id",
               foreignField: "albumId",
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0, // Remove the images array, keep only the count
            },
         },
         { $sort: sort },
         { $skip: skip },
         { $limit: limit },
      ];

      const [albums, total] = await Promise.all([
         albumCollection.aggregate(pipeline).toArray(),
         albumCollection.countDocuments(),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: albums as AlbumWithStats[],
         pagination,
      };
   } catch (error) {
      console.error("Error fetching albums:", error);
      throw new Error("Failed to fetch albums");
   }
}

/**
 * Get album by ID
 */
export async function getAlbumById(id: string): Promise<AlbumWithStats | null> {
   try {
      const albumCollection = await getCollection<Album>("albums");

      const pipeline = [
         { $match: { _id: new ObjectId(id) } },
         {
            $lookup: {
               from: "images",
               localField: "_id",
               foreignField: "albumId",
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
      ];

      const result = await albumCollection.aggregate(pipeline).toArray();
      return (result[0] as AlbumWithStats) || null;
   } catch (error) {
      console.error("Error fetching album by ID:", error);
      throw new Error("Failed to fetch album");
   }
}

/**
 * Create a new album
 */
export async function createAlbum(input: CreateAlbumInput): Promise<Album> {
   try {
      // Validate input
      const errors = validateAlbumInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      const collection = await getCollection<Album>("albums");
      const albumData = createAlbumMetadata(input);

      const result = await collection.insertOne(albumData);

      if (!result.insertedId) {
         throw new Error("Failed to create album");
      }

      const createdAlbum = await collection.findOne({ _id: result.insertedId });

      if (!createdAlbum) {
         throw new Error("Failed to retrieve created album");
      }

      return createdAlbum;
   } catch (error) {
      console.error("Error creating album:", error);
      throw error;
   }
}

/**
 * Update an album
 */
export async function updateAlbum(
   id: string,
   input: UpdateAlbumInput
): Promise<Album | null> {
   try {
      const collection = await getCollection<Album>("albums");

      const updateData = {
         ...input,
         updatedAt: new Date(),
      };

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      return result || null;
   } catch (error) {
      console.error("Error updating album:", error);
      throw new Error("Failed to update album");
   }
}

/**
 * Delete an album
 */
export async function deleteAlbum(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<Album>("albums");
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error deleting album:", error);
      throw new Error("Failed to delete album");
   }
}

/**
 * Get public albums only
 */
export async function getPublicAlbums(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<AlbumWithStats>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const albumCollection = await getCollection<Album>("albums");

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Get public albums with image counts
      const pipeline = [
         { $match: { isPublic: true } },
         {
            $lookup: {
               from: "images",
               localField: "_id",
               foreignField: "albumId",
               as: "images",
            },
         },
         {
            $addFields: {
               imageCount: { $size: "$images" },
            },
         },
         {
            $project: {
               images: 0,
            },
         },
         { $sort: sort },
         { $skip: skip },
         { $limit: limit },
      ];

      const [albums, total] = await Promise.all([
         albumCollection.aggregate(pipeline).toArray(),
         albumCollection.countDocuments({ isPublic: true }),
      ]);

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: albums as AlbumWithStats[],
         pagination,
      };
   } catch (error) {
      console.error("Error fetching public albums:", error);
      throw new Error("Failed to fetch public albums");
   }
}
