import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Filter, Grid, ImageIcon, Search, Upload } from "lucide-react";

export default function UngroupedPage() {
   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-3xl font-bold text-foreground">
                     Ungrouped Images
                  </h1>
                  <p className="text-muted-foreground">
                     Images that haven&apos;t been assigned to albums yet
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-3">
               <Button variant="outline" size="sm">
                  <Grid className="w-4 h-4 mr-2" />
                  Grid View
               </Button>
               <Button variant="outline" size="sm">
                  <Search className="w-4 h-4 mr-2" />
                  Search
               </Button>
               <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
               </Button>
               <Button
                  size="sm"
                  className="bg-gradient-accent hover:opacity-90"
               >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Images
               </Button>
            </div>
         </div>

         {/* Main Content */}
         <Card className="border-border/50">
            <CardHeader>
               <CardTitle className="text-foreground">
                  Ungrouped Images
               </CardTitle>
               <CardDescription>
                  Manage and organize images that aren&apos;t assigned to any
                  album
               </CardDescription>
            </CardHeader>
            <CardContent>
               <div className="flex items-center justify-center h-64 text-muted-foreground">
                  <div className="text-center">
                     <ImageIcon className="w-16 h-16 mx-auto mb-4 opacity-50" />
                     <h3 className="text-lg font-medium mb-2">
                        No ungrouped images
                     </h3>
                     <p className="text-sm mb-4">
                        Upload some images or check if all images are properly
                        organized in albums
                     </p>
                     <Button className="bg-gradient-accent hover:opacity-90">
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Images
                     </Button>
                  </div>
               </div>
            </CardContent>
         </Card>
      </div>
   );
}
