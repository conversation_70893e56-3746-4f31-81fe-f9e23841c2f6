import { PaginationOptions } from "@/lib/models";

/**
 * Query key factory for consistent query key generation
 */
export const queryKeys = {
  // Images
  images: {
    all: ['images'] as const,
    lists: () => [...queryKeys.images.all, 'list'] as const,
    list: (filters: PaginationOptions & { albumId?: string | null; collectionId?: string | null }) => 
      [...queryKeys.images.lists(), filters] as const,
    ungrouped: (filters: PaginationOptions) => 
      [...queryKeys.images.all, 'ungrouped', filters] as const,
    detail: (id: string) => [...queryKeys.images.all, 'detail', id] as const,
    search: (query: string, filters: PaginationOptions) => 
      [...queryKeys.images.all, 'search', query, filters] as const,
  },
  
  // Albums
  albums: {
    all: ['albums'] as const,
    lists: () => [...queryKeys.albums.all, 'list'] as const,
    list: (filters: PaginationOptions) => [...queryKeys.albums.lists(), filters] as const,
    public: (filters: PaginationOptions) => [...queryKeys.albums.all, 'public', filters] as const,
    detail: (id: string) => [...queryKeys.albums.all, 'detail', id] as const,
  },
  
  // Collections
  collections: {
    all: ['collections'] as const,
    lists: () => [...queryKeys.collections.all, 'list'] as const,
    list: (filters: PaginationOptions) => [...queryKeys.collections.lists(), filters] as const,
    public: (filters: PaginationOptions) => [...queryKeys.collections.all, 'public', filters] as const,
    detail: (id: string) => [...queryKeys.collections.all, 'detail', id] as const,
  },
} as const;

/**
 * Invalidation helpers
 */
export const invalidationKeys = {
  // Invalidate all images
  allImages: () => queryKeys.images.all,
  
  // Invalidate specific image lists
  imagesList: () => queryKeys.images.lists(),
  ungroupedImages: () => [...queryKeys.images.all, 'ungrouped'],
  
  // Invalidate all albums
  allAlbums: () => queryKeys.albums.all,
  albumsList: () => queryKeys.albums.lists(),
  
  // Invalidate all collections
  allCollections: () => queryKeys.collections.all,
  collectionsList: () => queryKeys.collections.lists(),
} as const;
