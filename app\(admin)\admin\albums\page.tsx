import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Album, Filter, Plus, Search } from "lucide-react";

export default function AlbumsPage() {
   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-3xl font-bold text-foreground">Albums</h1>
                  <p className="text-muted-foreground">
                     Organize and manage your photo albums
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-3">
               <Button variant="outline" size="sm">
                  <Search className="w-4 h-4 mr-2" />
                  Search
               </Button>
               <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
               </Button>
               <Button
                  size="sm"
                  className="bg-gradient-accent hover:opacity-90"
               >
                  <Plus className="w-4 h-4 mr-2" />
                  New Album
               </Button>
            </div>
         </div>

         {/* Main Content */}
         <Card className="border-border/50">
            <CardHeader>
               <CardTitle className="text-foreground">
                  Album Management
               </CardTitle>
               <CardDescription>
                  Create, edit, and organize albums for your photography
                  collections
               </CardDescription>
            </CardHeader>
            <CardContent>
               <div className="flex items-center justify-center h-64 text-muted-foreground">
                  <div className="text-center">
                     <Album className="w-16 h-16 mx-auto mb-4 opacity-50" />
                     <h3 className="text-lg font-medium mb-2">No albums yet</h3>
                     <p className="text-sm mb-4">
                        Create your first album to start organizing your photos
                     </p>
                     <Button className="bg-gradient-accent hover:opacity-90">
                        <Plus className="w-4 h-4 mr-2" />
                        Create Album
                     </Button>
                  </div>
               </div>
            </CardContent>
         </Card>
      </div>
   );
}
