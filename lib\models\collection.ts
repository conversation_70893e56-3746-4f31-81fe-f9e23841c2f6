import { ObjectId } from "mongodb";

// Collection interface for database storage
export interface Collection {
  _id?: ObjectId;
  name: string;
  description?: string;
  color?: string; // Hex color for UI display
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Collection creation input
export interface CreateCollectionInput {
  name: string;
  description?: string;
  color?: string;
  isPublic?: boolean;
}

// Collection update input
export interface UpdateCollectionInput {
  name?: string;
  description?: string;
  color?: string;
  isPublic?: boolean;
}

// Collection with image count for display
export interface CollectionWithStats extends Collection {
  imageCount: number;
}

/**
 * Create collection metadata from input
 */
export function createCollectionMetadata(input: CreateCollectionInput): Omit<Collection, '_id'> {
  const now = new Date();
  
  return {
    name: input.name,
    description: input.description,
    color: input.color,
    isPublic: input.isPublic ?? true,
    createdAt: now,
    updatedAt: now,
  };
}

/**
 * Validate collection input
 */
export function validateCollectionInput(input: CreateCollectionInput): string[] {
  const errors: string[] = [];

  if (!input.name || typeof input.name !== 'string') {
    errors.push('Name is required and must be a string');
  }

  if (input.name && input.name.trim().length === 0) {
    errors.push('Name cannot be empty');
  }

  if (input.description !== undefined && typeof input.description !== 'string') {
    errors.push('Description must be a string');
  }

  if (input.color !== undefined && typeof input.color !== 'string') {
    errors.push('Color must be a string');
  }

  // Validate hex color format if provided
  if (input.color && !/^#[0-9A-F]{6}$/i.test(input.color)) {
    errors.push('Color must be a valid hex color (e.g., #FF0000)');
  }

  if (input.isPublic !== undefined && typeof input.isPublic !== 'boolean') {
    errors.push('isPublic must be a boolean');
  }

  return errors;
}
