"use server";

import {
   DeleteObjectCommand,
   PutObjectCommand,
   S3Client,
} from "@aws-sdk/client-s3";
import { generateUniqueKey } from "./helpers";

const {
   R2_ACCESS_ID,
   R2_ACCESS_KEY,
   R2_ACCOUNT_ID,
   R2_ENDPOINT,
   R2_BUCKET,
   R2_PUBLIC_URL,
} = process.env;

if (
   !R2_ACCESS_ID ||
   !R2_ACCESS_KEY ||
   !R2_ACCOUNT_ID ||
   !R2_ENDPOINT ||
   !R2_BUCKET ||
   !R2_PUBLIC_URL
) {
   throw new Error("Missing Cloudflare environment variables");
}

export interface FileObject {
   Key?: string;
   LastModified?: Date;
   ETag?: string;
   Size?: number;
   StorageClass?: string;
}

const R2 = new S3Client({
   region: "auto",
   endpoint: R2_ENDPOINT,
   credentials: {
      accessKeyId: R2_ACCESS_ID,
      secretAccessKey: R2_ACCESS_KEY,
   },
   forcePathStyle: false, // Cloudflare R2 uses virtual-hosted-style
   maxAttempts: 3, // Retry failed requests
   requestHandler: {
      connectionTimeout: 30000, // 30 seconds
      socketTimeout: 30000, // 30 seconds
   },
});

/**
 * Upload file buffer to R2 with specified key
 */
export async function uploadFile(
   file: Buffer,
   key: string,
   contentType?: string
) {
   const command = new PutObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
      Body: file,
      ContentType: contentType,
   });

   try {
      const response = await R2.send(command);
      return response;
   } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
   }
}

/**
 * Upload file directly from File object
 */
export async function uploadFileFromFile(
   file: File,
   key?: string
): Promise<{ url: string; key: string }> {
   try {
      const fileKey = key || generateUniqueKey(file.name);
      const buffer = Buffer.from(await file.arrayBuffer());

      await uploadFile(buffer, fileKey, file.type);

      // Generate public URL
      const url = `${R2_PUBLIC_URL}/${fileKey}`;

      return { url, key: fileKey };
   } catch (error) {
      console.error("Error uploading file:", error);
      throw error;
   }
}

export async function deleteFile(key: string) {
   const command = new DeleteObjectCommand({
      Bucket: R2_BUCKET,
      Key: key,
   });

   try {
      const response = await R2.send(command);
      return response;
   } catch (error) {
      console.error("Error deleting file:", error);
      throw error;
   }
}
