/**
 * Generate a unique key for file storage
 */
export function generateUnique<PERSON>ey(originalName: string): string {
   const timestamp = Date.now();
   const randomString = Math.random().toString(36).substring(2, 15);
   const extension = originalName.split(".").pop() || "";
   const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, "");

   // Sanitize filename
   const sanitizedName = nameWithoutExtension
      .replace(/[^a-zA-Z0-9-_]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

   return `gallery/${sanitizedName}-${timestamp}-${randomString}.${extension}`;
}
