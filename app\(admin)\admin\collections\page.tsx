import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Filter, Plus, Search, Tags } from "lucide-react";

export default function CollectionsPage() {
   return (
      <div className="p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-3xl font-bold text-foreground">
                     Collections
                  </h1>
                  <p className="text-muted-foreground">
                     Manage tags and collections for your images
                  </p>
               </div>
            </div>

            <div className="flex items-center space-x-3">
               <Button variant="outline" size="sm">
                  <Search className="w-4 h-4 mr-2" />
                  Search
               </Button>
               <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
               </Button>
               <Button
                  size="sm"
                  className="bg-gradient-accent hover:opacity-90"
               >
                  <Plus className="w-4 h-4 mr-2" />
                  New Collection
               </Button>
            </div>
         </div>

         {/* Main Content */}
         <Card className="border-border/50">
            <CardHeader>
               <CardTitle className="text-foreground">
                  Collection Management
               </CardTitle>
               <CardDescription>
                  Create and manage collections to categorize your images across
                  albums
               </CardDescription>
            </CardHeader>
            <CardContent>
               <div className="flex items-center justify-center h-64 text-muted-foreground">
                  <div className="text-center">
                     <Tags className="w-16 h-16 mx-auto mb-4 opacity-50" />
                     <h3 className="text-lg font-medium mb-2">
                        No collections yet
                     </h3>
                     <p className="text-sm mb-4">
                        Create collections to tag and categorize your images
                     </p>
                     <Button className="bg-gradient-accent hover:opacity-90">
                        <Plus className="w-4 h-4 mr-2" />
                        Create Collection
                     </Button>
                  </div>
               </div>
            </CardContent>
         </Card>
      </div>
   );
}
