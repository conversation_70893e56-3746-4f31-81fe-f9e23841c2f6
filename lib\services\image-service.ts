"use server";

import {
   CreateImageInput,
   DEFAULT_PAGINATION,
   Image,
   ImageDocument,
   PaginatedResponse,
   PaginationOptions,
   UpdateImageInput,
   createImageMetadata,
   createPaginationMetadata,
   validateImageInput,
} from "@/lib/models";
import { getCollection } from "@/lib/mongodb/db";
import { ObjectId } from "mongodb";

/**
 * Get all images with pagination and filtering
 */
export async function getImages(
   options: PaginationOptions & {
      albumId?: string | null;
      collectionId?: string | null;
   } = {}
): Promise<PaginatedResponse<Image>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
         albumId,
         collectionId,
      } = options;

      const collection = await getCollection<ImageDocument>("images");

      // Build filter query
      const filter: Record<string, string | null | undefined> = {};

      if (albumId !== undefined) {
         filter.albumId = albumId;
      }

      if (collectionId !== undefined) {
         filter.collectionId = collectionId;
      }

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Execute queries
      const [images, total] = await Promise.all([
         collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
         collection.countDocuments(filter),
      ]);

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: serializedImages,
         pagination,
      };
   } catch (error) {
      console.error("Error fetching images:", error);
      throw new Error("Failed to fetch images");
   }
}

/**
 * Get ungrouped images (albumId is null)
 */
export async function getUngroupedImages(
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   return getImages({ ...options, albumId: null });
}

/**
 * Get image by ID
 */
export async function getImageById(id: string): Promise<Image | null> {
   try {
      const collection = await getCollection<ImageDocument>("images");
      const image = await collection.findOne({ _id: new ObjectId(id) });

      if (!image) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...image,
         _id: image._id?.toString(),
      };
   } catch (error) {
      console.error("Error fetching image by ID:", error);
      throw new Error("Failed to fetch image");
   }
}

/**
 * Create a new image
 */
export async function createImage(input: CreateImageInput): Promise<Image> {
   try {
      // Validate input
      const errors = validateImageInput(input);
      if (errors.length > 0) {
         throw new Error(`Validation failed: ${errors.join(", ")}`);
      }

      const collection = await getCollection<ImageDocument>("images");
      const imageData = createImageMetadata(input);

      const result = await collection.insertOne(imageData);

      if (!result.insertedId) {
         throw new Error("Failed to create image");
      }

      const createdImage = await collection.findOne({ _id: result.insertedId });

      if (!createdImage) {
         throw new Error("Failed to retrieve created image");
      }

      // Convert ObjectId to string for client serialization
      return {
         ...createdImage,
         _id: createdImage._id?.toString(),
      };
   } catch (error) {
      console.error("Error creating image:", error);
      throw error;
   }
}

/**
 * Update an image
 */
export async function updateImage(
   id: string,
   input: UpdateImageInput
): Promise<Image | null> {
   try {
      const collection = await getCollection<ImageDocument>("images");

      const updateData = {
         ...input,
         updatedAt: new Date(),
      };

      const result = await collection.findOneAndUpdate(
         { _id: new ObjectId(id) },
         { $set: updateData },
         { returnDocument: "after" }
      );

      if (!result) return null;

      // Convert ObjectId to string for client serialization
      return {
         ...result,
         _id: result._id?.toString(),
      };
   } catch (error) {
      console.error("Error updating image:", error);
      throw new Error("Failed to update image");
   }
}

/**
 * Delete an image
 */
export async function deleteImage(id: string): Promise<boolean> {
   try {
      const collection = await getCollection<ImageDocument>("images");
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount > 0;
   } catch (error) {
      console.error("Error deleting image:", error);
      throw new Error("Failed to delete image");
   }
}

/**
 * Get images by album ID
 */
export async function getImagesByAlbumId(
   albumId: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   return getImages({ ...options, albumId });
}

/**
 * Get images by collection ID
 */
export async function getImagesByCollectionId(
   collectionId: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   return getImages({ ...options, collectionId });
}

/**
 * Search images by name
 */
export async function searchImages(
   query: string,
   options: PaginationOptions = {}
): Promise<PaginatedResponse<Image>> {
   try {
      const {
         page = DEFAULT_PAGINATION.page,
         limit = DEFAULT_PAGINATION.limit,
         sortBy = DEFAULT_PAGINATION.sortBy,
         sortOrder = DEFAULT_PAGINATION.sortOrder,
      } = options;

      const collection = await getCollection<ImageDocument>("images");

      // Build search filter
      const filter = {
         name: { $regex: query, $options: "i" },
      };

      // Build sort query
      const sort: Record<string, 1 | -1> = {};
      sort[sortBy] = sortOrder === "asc" ? 1 : -1;

      // Calculate skip value
      const skip = (page - 1) * limit;

      // Execute queries
      const [images, total] = await Promise.all([
         collection.find(filter).sort(sort).skip(skip).limit(limit).toArray(),
         collection.countDocuments(filter),
      ]);

      // Convert ObjectIds to strings for client serialization
      const serializedImages = images.map((image) => ({
         ...image,
         _id: image._id?.toString(),
      }));

      const pagination = createPaginationMetadata(page, limit, total);

      return {
         data: serializedImages,
         pagination,
      };
   } catch (error) {
      console.error("Error searching images:", error);
      throw new Error("Failed to search images");
   }
}
